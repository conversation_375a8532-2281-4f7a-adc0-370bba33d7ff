@plugin "tailwindcss-safe-area";

@source "../../node_modules/@daveyplate/better-auth-ui";

@layer base {
	button:not(:disabled),
	[role='button']:not(:disabled) {
		cursor: pointer;
	}

	/* Remove shadows from most elements but preserve form validation and focus states */
	*:not(input):not(textarea):not(select):not([role='button']):not(button):not([data-slot]):not(
			[aria-invalid]
		):not(:focus-visible) {
		box-shadow: none !important;
	}
}

[role='menuitem']:not(:disabled) {
	cursor: pointer;
}

:root {
	--warning: hsl(38 92% 50%);
	--warning-foreground: hsl(48 96% 89%);
}

.dark {
	--warning: hsl(48 96% 89%);
	--warning-foreground: hsl(38 92% 50%);
}

@theme inline {
	--color-warning: var(--warning);
	--color-warning-foreground: var(--warning-foreground);
}

/** iOS Dynamic System Font Scaling */
/* @supports (-webkit-touch-callout:none) {
	html {
		font: -apple-system-body;
	}
} */
